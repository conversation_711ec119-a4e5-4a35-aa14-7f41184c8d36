import bcrypt
import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("app.log", mode="a"),
    ],
)
logger = logging.getLogger(__name__)


def generate_password_hash():
    """Generate a bcrypt hash for the niche deletion password"""
    password = input("Enter niche deletion password: ")
    hashed = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
    print(f"Add to .env file:\nPASSWORD_HASH={hashed}")
    logger.info("Generated bcrypt hash for password")


if __name__ == "__main__":
    generate_password_hash()
