import streamlit as st
import os
from datetime import datetime
from managers.niche_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from managers.quote_manager import QuoteManager
from managers.tab_manager import TabManager
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("app.log", mode="a"),
    ],
)
logger = logging.getLogger(__name__)


def load_css():
    """Load custom CSS for styling the app"""
    st.markdown(
        """
        <style>
        /* Main app styling */
        .main-header {
            text-align: center;
            background: linear-gradient(45deg, #2F80ED, #EC4899);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        .selected-niche {
            font-size: 1.2em;
            color: #2F80ED;
            padding: 0.5rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        .stat-card {
            background: linear-gradient(135deg, #2F80ED 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 1rem;
        }
        .quote-preview {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-style: italic;
            font-size: 1.1em;
        }
        .instagram-preview {
            background: white;
            border: 1px solid #dbdbdb;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
            margin: 1rem 0;
        }
        .quote-scroll-container {
            overflow-y: auto;
            max-height: 400px;
            border: 1px solid #e2e8f0;
            padding: 10px;
            border-radius: 5px;
        }
        .quote-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 5px;
            cursor: pointer;
        }
        .quote-item:hover {
            background-color: #e3f2fd;
        }
        [data-testid="stRadio"] > div > div {
            background-color: #ffffff;
            margin-bottom: 0.25rem;
        }
        [data-testid="stRadio"] label {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 0.5rem;
            font-size: 1em;
            color: #333333;
        }
        [data-testid="stRadio"] input {
            margin-right: 0.5rem;
        }
        [data-testid="stTabs"] > div > button {
            background-color: #FFFFFF;
            color: #333333;
            border: none;
            padding: 10px 20px;
            font-weight: normal;
            transition: all 0.2s;
        }
        [data-testid="stTabs"] > div > button:hover {
            background-color: #F7F7F7;
            border-bottom: 2px solid #2F80ED;
        }
        [data-testid="stTabs"] > div > button[aria-selected="true"] {
            font-weight: bold;
            border-bottom: 2px solid #2F80ED;
            color: #2F80ED;
        }
        textarea, input[type="text"], input[type="password"] {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 8px;
            font-size: 1em;
            background: #fafafa;
        }
        textarea:focus, input[type="text"]:focus, input[type="password"]:focus {
            border-color: #2F80ED;
            outline: none;
        }
        button[data-testid="stButton"] {
            background-color: #f0f0f0;
            border-color: #e0e0e0;
            color: #333333;
        }
        button[data-testid="stButton"]:hover {
            background-color: #e0e2e0;
            border-color: #2F80ED;
        }
        @media (max-width: 768px) {
            .main-header { padding: 1.5rem; }
            .stat-card { padding: 0.75rem; }
            .quote-preview, .instagram-preview { padding: 1rem; margin: 0.5rem 0; }
            textarea, input[type="text"], input[type="password"] { font-size: 0.9em; }
            .quote-item { flex-direction: column; align-items: flex-start; }
            [data-testid="stTabs"] > div > button { padding: 8px 10px; font-size: 0.9em; }
        }
        </style>
        """,
        unsafe_allow_html=True,
    )


def main():
    """Main function to run the Instagram Quote Generator App"""
    st.set_page_config(page_title="Instagram Quote Generator", layout="wide")
    load_css()

    # Initialize session state
    if "selected_niche" not in st.session_state:
        st.session_state.selected_niche = "Growth Mindset"
    if "active_tab" not in st.session_state:
        st.session_state.active_tab = "Niche Management"
    if "navigate_to_management" not in st.session_state:
        st.session_state.navigate_to_management = False
    if "navigate_to_caption" not in st.session_state:
        st.session_state.navigate_to_caption = False
    if "show_prompt" not in st.session_state:
        st.session_state.show_prompt = False
    if "show_caption_prompt" not in st.session_state:
        st.session_state.show_caption_prompt = False
    if "edit_niche" not in st.session_state:
        st.session_state.edit_niche = False
    if "edit_quotes" not in st.session_state:
        st.session_state.edit_quotes = False
    if "edit_caption" not in st.session_state:
        st.session_state.edit_caption = False

    # Handle navigation flags
    if st.session_state.navigate_to_management:
        st.session_state.active_tab = "Quote Management"
        st.session_state.navigate_to_management = False
    if st.session_state.navigate_to_caption:
        st.session_state.active_tab = "Caption Generator"
        st.session_state.navigate_to_caption = False

    # Initialize managers
    try:
        niche_manager = NicheManager()
        quote_manager = QuoteManager(niche_manager)
        tab_manager = TabManager(quote_manager, niche_manager)
    except Exception as e:
        st.error(f"Failed to initialize app: {e}")
        logger.error(f"Initialization error: {e}")
        return

    # Create necessary directories
    os.makedirs("data", exist_ok=True)
    os.makedirs("backups", exist_ok=True)

    # Display main header
    st.markdown(
        '<div class="main-header"><h1>Instagram Quote Generator</h1><p>Manage niches, generate quotes, and create Instagram content</p></div>',
        unsafe_allow_html=True,
    )

    # Display selected niche
    st.markdown(
        f'<div class="selected-niche">Selected Niche: {st.session_state.selected_niche}</div>',
        unsafe_allow_html=True,
    )

    # Define tabs
    tab_names = [
        "🎯 Niche Management",
        "📝 Quote Generator",
        "📊 Quote Management",
        "🎨 Caption Generator",
    ]
    tabs = st.tabs(tab_names)

    # Render tabs
    with tabs[0]:
        tab_manager.render_niche_management_tab()
    with tabs[1]:
        if st.session_state.selected_niche:
            tab_manager.render_quote_generator_tab()
        else:
            st.warning("Please select a niche first.")
    with tabs[2]:
        if st.session_state.selected_niche:
            tab_manager.render_quote_management_tab()
        else:
            st.warning("Please select a niche first.")
    with tabs[3]:
        if st.session_state.selected_niche:
            tab_manager.render_caption_generator_tab()
        else:
            st.warning("Please select a niche first.")


if __name__ == "__main__":
    main()
