import streamlit as st
import pyperclip
import html
import logging
from ui.components import UIComponents

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("tab_manager.log", mode="a"),
    ],
)
logger = logging.getLogger(__name__)


class TabManager:
    """Manages rendering of Streamlit tabs for the Instagram Quote Generator App"""

    def __init__(self, quote_manager, niche_manager):
        self.quote_manager = quote_manager
        self.niche_manager = niche_manager

    def _render_niche_selector(self, tab_key: str):
        """Render niche selection dropdown with a unique key for each tab"""
        niches = self.niche_manager.load_niches()
        niche_names = [niche["name"] for niche in niches]
        if not niche_names:
            st.warning(
                "No niches available. Please add a niche in the Niche Management tab."
            )
            return
        selected = st.selectbox(
            "Select Niche",
            niche_names,
            index=(
                niche_names.index(st.session_state.selected_niche)
                if st.session_state.selected_niche in niche_names
                else 0
            ),
            key=f"niche_selector_{tab_key}",
        )
        if selected != st.session_state.selected_niche:
            st.session_state.selected_niche = selected
            st.rerun()

    def render_niche_management_tab(self):
        """Render the Niche Management tab"""
        st.subheader("Niche Management")
        self._render_niche_selector("niche_management")
        niche_data = self.niche_manager.get_niche_data(st.session_state.selected_niche)

        if niche_data:
            st.markdown('<div class="stat-card">', unsafe_allow_html=True)
            st.write(f"**Niche**: {niche_data['name']}")
            st.write(
                f"**Quote Prompt**: {html.escape(niche_data['quote_prompt'])}"
            )
            st.write(
                f"**Caption Prompt**: {html.escape(niche_data['caption_prompt'])}"
            )
            st.markdown("</div>", unsafe_allow_html=True)

        st.markdown("---")
        st.subheader("Manage Niches")

        # Add Niche
        with st.expander("Add New Niche"):
            with st.form("add_niche_form"):
                name = st.text_input(
                    "Niche Name (max 15 chars, alphanumeric/spaces)", max_chars=15
                )
                quote_prompt = st.text_area("Quote Prompt", height=100)
                caption_prompt = st.text_area("Caption Prompt", height=100)
                if st.form_submit_button("Add Niche"):
                    try:
                        self.niche_manager.add_niche(name, quote_prompt, caption_prompt)
                        st.markdown(
                            '<div class="success-message">Niche added successfully!</div>',
                            unsafe_allow_html=True,
                        )
                        logger.info(f"Added niche: {name}")
                        st.rerun()
                    except ValueError as e:
                        st.error(str(e))
                        logger.error(f"Failed to add niche {name}: {e}")

        # Edit Niche
        if niche_data and st.session_state.get("edit_niche", False):
            with st.expander(f"Edit Niche: {niche_data['name']}", expanded=True):
                with st.form("edit_niche_form"):
                    new_name = st.text_input(
                        "Niche Name", value=niche_data["name"], max_chars=15
                    )
                    new_quote_prompt = st.text_area(
                        "Quote Prompt", value=niche_data["quote_prompt"], height=100
                    )
                    new_caption_prompt = st.text_area(
                        "Caption Prompt", value=niche_data["caption_prompt"], height=100
                    )
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.form_submit_button("Update Niche"):
                            try:
                                self.niche_manager.update_niche(
                                    niche_data["name"],
                                    new_name,
                                    new_quote_prompt,
                                    new_caption_prompt,
                                )
                                st.session_state.edit_niche = False
                                st.session_state.selected_niche = new_name
                                st.markdown(
                                    '<div class="success-message">Niche updated successfully!</div>',
                                    unsafe_allow_html=True,
                                )
                                logger.info(
                                    f"Updated niche: {niche_data['name']} to {new_name}"
                                )
                                st.rerun()
                            except ValueError as e:
                                st.error(str(e))
                                logger.error(
                                    f"Failed to update niche {niche_data['name']}: {e}"
                                )
                    with col2:
                        if st.form_submit_button("Cancel"):
                            st.session_state.edit_niche = False
                            st.rerun()
        else:
            if niche_data and st.button("Edit Selected Niche"):
                st.session_state.edit_niche = True
                st.rerun()

        # Delete Niche
        with st.expander("Delete Niche"):
            with st.form("delete_niche_form"):
                password = st.text_input("Enter Password", type="password")
                if st.form_submit_button("Delete Selected Niche"):
                    if self.niche_manager.delete_niche(
                        st.session_state.selected_niche, password
                    ):
                        st.markdown(
                            '<div class="success-message">Niche deleted successfully!</div>',
                            unsafe_allow_html=True,
                        )
                        logger.info(f"Deleted niche: {st.session_state.selected_niche}")
                        niches = self.niche_manager.load_niches()
                        st.session_state.selected_niche = (
                            niches[0]["name"] if niches else "Growth Mindset"
                        )
                        st.rerun()
                    else:
                        st.error("Invalid password or niche not found.")
                        logger.warning(
                            f"Failed to delete niche: {st.session_state.selected_niche}"
                        )

        # Manual Backup
        if st.button("Manual Backup All Niches"):
            backed_files = self.niche_manager.manual_backup_all()
            if backed_files:
                st.markdown(
                    '<div class="success-message">Backup completed: '
                    + ", ".join(backed_files)
                    + "</div>",
                    unsafe_allow_html=True,
                )
                logger.info(f"Manual backup completed: {backed_files}")
            else:
                st.warning("No files were backed up.")
                logger.warning("Manual backup resulted in no files.")

    def render_quote_generator_tab(self):
        """Render the Quote Generator tab"""
        st.subheader("Quote Generator")
        self._render_niche_selector("quote_generator")
        niche_data = self.niche_manager.get_niche_data(st.session_state.selected_niche)

        if niche_data:
            total, generated, uploaded = self.quote_manager.get_quote_stats(
                st.session_state.selected_niche
            )
            col1, col2, col3 = st.columns(3)
            with col1:
                UIComponents.render_stat_card(
                    "Total Quotes", total, f"{st.session_state.selected_niche}_total"
                )
            with col2:
                UIComponents.render_stat_card(
                    "Generated",
                    generated,
                    f"{st.session_state.selected_niche}_generated",
                )
            with col3:
                UIComponents.render_stat_card(
                    "Uploaded", uploaded, f"{st.session_state.selected_niche}_uploaded"
                )

            st.markdown("---")
            num_quotes = st.number_input(
                "Number of Quotes to Generate", min_value=1, max_value=10, value=3
            )
            if st.button("Generate Quote Prompt"):
                try:
                    prompt = self.quote_manager.generate_prompt(
                        st.session_state.selected_niche, num_quotes
                    )
                    st.session_state.show_prompt = True
                    st.session_state.prompt_text = prompt
                    logger.info(
                        f"Generated quote prompt for {st.session_state.selected_niche}"
                    )
                except ValueError as e:
                    st.error(str(e))
                    logger.error(f"Failed to generate prompt: {e}")

            if st.session_state.get("show_prompt", False):
                st.text_area(
                    "Quote Prompt",
                    st.session_state.prompt_text,
                    height=150,
                    key="quote_prompt_display",
                )
                if st.button("Copy Prompt"):
                    pyperclip.copy(st.session_state.prompt_text)
                    st.markdown(
                        '<div class="success-message">Prompt copied to clipboard!</div>',
                        unsafe_allow_html=True,
                    )
                    logger.info(
                        f"Copied quote prompt for {st.session_state.selected_niche}"
                    )

            with st.form("add_quotes_form"):
                quotes_input = st.text_area(
                    "Paste Generated Quotes (one per line)", height=200
                )
                if st.form_submit_button("Add Quotes"):
                    quotes_list = [
                        q.strip() for q in quotes_input.split("\n") if q.strip()
                    ]
                    if quotes_list:
                        try:
                            added = self.quote_manager.add_quotes(
                                quotes_list, st.session_state.selected_niche
                            )
                            st.markdown(
                                f'<div class="success-message">Added {added} quotes successfully!</div>',
                                unsafe_allow_html=True,
                            )
                            logger.info(
                                f"Added {added} quotes to {st.session_state.selected_niche}"
                            )
                            st.rerun()
                        except Exception as e:
                            st.error(f"Failed to add quotes: {e}")
                            logger.error(f"Failed to add quotes: {e}")
                    else:
                        st.warning("Please enter at least one quote.")
                        logger.warning("No quotes provided for addition")

    def render_quote_management_tab(self):
        """Render the Quote Management tab"""
        st.subheader("Quote Management")
        self._render_niche_selector("quote_management")
        niche_data = self.niche_manager.get_niche_data(st.session_state.selected_niche)

        if niche_data:
            total, generated, uploaded = self.quote_manager.get_quote_stats(
                st.session_state.selected_niche
            )
            col1, col2, col3 = st.columns(3)
            with col1:
                UIComponents.render_stat_card(
                    "Total Quotes", total, f"{st.session_state.selected_niche}_total"
                )
            with col2:
                UIComponents.render_stat_card(
                    "Generated",
                    generated,
                    f"{st.session_state.selected_niche}_generated",
                )
            with col3:
                UIComponents.render_stat_card(
                    "Uploaded", uploaded, f"{st.session_state.selected_niche}_uploaded"
                )

            st.markdown("---")
            quotes_data = self.quote_manager.load_quotes(
                st.session_state.selected_niche
            )
            quotes = quotes_data.get("quotes", [])

            if quotes:
                st.markdown(
                    '<div class="quote-scroll-container">', unsafe_allow_html=True
                )
                selected_quote = st.radio(
                    "Select Quote",
                    options=[
                        f"#{q['number']} - {html.escape(q['text'])[:50]}..."
                        for q in quotes
                    ],
                    format_func=lambda x: x,
                    key="quote_selector",
                )
                quote_number = selected_quote.split(" - ")[0][
                    1:
                ]  # Extract number (e.g., "1" from "#1 - text...")
                quote = self.quote_manager.get_quote_by_number(
                    st.session_state.selected_niche, quote_number
                )

                if quote:
                    UIComponents.render_quote_preview(quote, f"quote_{quote['number']}")
                    # Display additional quote details (already in preview, but kept for consistency)
                    st.write(f"**Status**: {quote['status'].capitalize()}")
                    st.write(f"**Created**: {quote['created_date']}")
                    if quote["uploaded_date"]:
                        st.write(f"**Uploaded**: {quote['uploaded_date']}")

                    # Edit Quote Status/Content
                    if st.session_state.get("edit_quotes", False):
                        with st.form("edit_quote_form"):
                            new_status = st.selectbox(
                                "Status",
                                ["generated", "uploaded"],
                                index=["generated", "uploaded"].index(quote["status"]),
                            )
                            new_caption = st.text_area(
                                "Caption", value=quote["caption"], height=100
                            )
                            new_hashtags = st.text_area(
                                "Hashtags", value=quote["hashtags"], height=100
                            )
                            col1, col2 = st.columns(2)
                            with col1:
                                if st.form_submit_button("Update Quote"):
                                    try:
                                        if new_status != quote["status"]:
                                            self.quote_manager.update_quote_status(
                                                st.session_state.selected_niche,
                                                quote_number,
                                                new_status,
                                            )
                                        if (
                                            new_caption != quote["caption"]
                                            or new_hashtags != quote["hashtags"]
                                        ):
                                            self.quote_manager.update_quote_content(
                                                st.session_state.selected_niche,
                                                quote_number,
                                                new_caption,
                                                new_hashtags,
                                            )
                                        st.session_state.edit_quotes = False
                                        st.markdown(
                                            '<div class="success-message">Quote updated successfully!</div>',
                                            unsafe_allow_html=True,
                                        )
                                        logger.info(
                                            f"Updated quote #{quote_number} in {st.session_state.selected_niche}"
                                        )
                                        st.rerun()
                                    except ValueError as e:
                                        st.error(str(e))
                                        logger.error(
                                            f"Failed to update quote #{quote_number}: {e}"
                                        )
                            with col2:
                                if st.form_submit_button("Cancel"):
                                    st.session_state.edit_quotes = False
                                    st.rerun()
                    else:
                        if st.button("Edit Quote"):
                            st.session_state.edit_quotes = True
                            st.rerun()

                    # Delete Quote
                    with st.form("delete_quote_form"):
                        password = st.text_input(
                            "Enter Password to Delete", type="password"
                        )
                        if st.form_submit_button("Delete Quote"):
                            if self.quote_manager.delete_quote(
                                st.session_state.selected_niche, quote_number, password
                            ):
                                st.markdown(
                                    '<div class="success-message">Quote deleted successfully!</div>',
                                    unsafe_allow_html=True,
                                )
                                logger.info(
                                    f"Deleted quote #{quote_number} from {st.session_state.selected_niche}"
                                )
                                st.rerun()
                            else:
                                st.error("Invalid password or quote not found.")
                                logger.warning(
                                    f"Failed to delete quote #{quote_number}"
                                )
                st.markdown("</div>", unsafe_allow_html=True)
            else:
                st.info("No quotes available for this niche.")

    def render_caption_generator_tab(self):
        """Render the Caption Generator tab"""
        st.subheader("Caption Generator")
        self._render_niche_selector("caption_generator")
        niche_data = self.niche_manager.get_niche_data(st.session_state.selected_niche)

        if niche_data:
            quotes_data = self.quote_manager.load_quotes(
                st.session_state.selected_niche
            )
            quotes = quotes_data.get("quotes", [])
            if quotes:
                quote_options = [
                    f"#{q['number']} - {html.escape(q['text'])[:50]}..." for q in quotes
                ]
                selected_quote = st.selectbox(
                    "Select Quote", quote_options, key="caption_quote_selector"
                )
                quote_number = selected_quote.split(" - ")[0][1:]
                quote = self.quote_manager.get_quote_by_number(
                    st.session_state.selected_niche, quote_number
                )

                if quote:
                    UIComponents.render_quote_preview(quote, f"quote_{quote['number']}")
                    if st.button("Generate Caption Prompt"):
                        try:
                            prompt = niche_data["caption_prompt"].format(
                                quote_text=html.escape(quote["text"])
                            )
                            st.session_state.show_caption_prompt = True
                            st.session_state.caption_prompt_text = prompt
                            logger.info(
                                f"Generated caption prompt for quote #{quote_number}"
                            )
                        except Exception as e:
                            st.error(f"Failed to generate caption prompt: {e}")
                            logger.error(f"Failed to generate caption prompt: {e}")

                    if st.session_state.get("show_caption_prompt", False):
                        st.text_area(
                            "Caption Prompt",
                            st.session_state.caption_prompt_text,
                            height=150,
                            key="caption_prompt_display",
                        )
                        if st.button("Copy Caption Prompt"):
                            pyperclip.copy(st.session_state.caption_prompt_text)
                            st.markdown(
                                '<div class="success-message">Caption prompt copied to clipboard!</div>',
                                unsafe_allow_html=True,
                            )
                            logger.info(
                                f"Copied caption prompt for quote #{quote_number}"
                            )

                    with st.form("edit_caption_form"):
                        caption = st.text_area(
                            "Instagram Caption", value=quote["caption"], height=100
                        )
                        hashtags = st.text_area(
                            "Hashtags", value=quote["hashtags"], height=100
                        )
                        if st.form_submit_button("Update Caption & Hashtags"):
                            try:
                                self.quote_manager.update_quote_content(
                                    st.session_state.selected_niche,
                                    quote_number,
                                    caption,
                                    hashtags,
                                )
                                st.markdown(
                                    '<div class="success-message">Caption and hashtags updated successfully!</div>',
                                    unsafe_allow_html=True,
                                )
                                logger.info(
                                    f"Updated caption for quote #{quote_number}"
                                )
                                st.rerun()
                            except ValueError as e:
                                st.error(str(e))
                                logger.error(
                                    f"Failed to update caption for quote #{quote_number}: {e}"
                                )

                    UIComponents.render_instagram_preview(
                        quote, f"instagram_{quote['number']}"
                    )
            else:
                st.info(
                    "No quotes available for this niche. Generate quotes in the Quote Generator tab."
                )
