import json
import os
import html
from datetime import datetime
import bcrypt
from dotenv import load_dotenv
import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("quote_manager.log", mode="a"),
    ],
)
logger = logging.getLogger(__name__)

load_dotenv()


class QuoteManager:
    """Manages quote storage, status, and statistics for each niche"""

    def __init__(self, niche_manager):
        self.niche_manager = niche_manager

    def load_quotes(self, niche_name: str):
        """Load quotes for a specific niche"""
        niche = self.niche_manager.get_niche_data(niche_name)
        if not niche or not os.path.exists(niche["quote_file"]):
            logger.warning(f"Quote file not found for niche {niche_name}")
            return {"quotes": []}
        try:
            with open(niche["quote_file"], "r") as f:
                data = json.load(f)
            for quote in data.get("quotes", []):
                # Ensure all required fields exist
                if "number" not in quote:
                    quote["number"] = str(quote.get("number", 0))
                if "status" not in quote:
                    quote["status"] = "generated"
                if "caption" not in quote:
                    quote["caption"] = ""
                if "hashtags" not in quote:
                    quote["hashtags"] = ""
                if "niche" not in quote:
                    quote["niche"] = niche_name
                if "created_date" not in quote:
                    quote["created_date"] = datetime.now().strftime("%d-%b-%Y")
                if "uploaded_date" not in quote:
                    quote["uploaded_date"] = None
            logger.debug(
                f"Loaded {len(data.get('quotes', []))} quotes for {niche_name}"
            )
            return data
        except Exception as e:
            logger.error(f"Failed to load quotes for {niche_name}: {e}")
            return {"quotes": []}

    def save_quotes(self, niche_name: str, data: dict):
        """Save quotes for a specific niche"""
        niche = self.niche_manager.get_niche_data(niche_name)
        if not niche:
            raise ValueError(f"Niche {niche_name} not found")
        try:
            with open(niche["quote_file"], "w") as f:
                json.dump(data, f, indent=4)
            logger.info(f"Saved quotes for {niche_name}")
        except Exception as e:
            logger.error(f"Failed to save quotes for {niche_name}: {e}")
            raise

    def add_quotes(self, quotes_list: list, niche_name: str):
        """Add multiple quotes to a niche with unique numbering"""
        data = self.load_quotes(niche_name)
        quotes = data["quotes"]
        max_number = max([int(q.get("number", 0)) for q in quotes], default=0)
        added_count = 0
        try:
            for quote_text in quotes_list:
                if not quote_text.strip():
                    continue
                if not any(q["text"] == quote_text for q in quotes):
                    max_number += 1
                    new_quote = {
                        "number": str(max_number),
                        "text": html.unescape(quote_text),
                        "status": "generated",
                        "niche": niche_name,
                        "created_date": datetime.now().strftime("%d-%b-%Y"),
                        "uploaded_date": None,
                        "caption": "",
                        "hashtags": "",
                    }
                    quotes.append(new_quote)
                    added_count += 1
            if added_count > 0:
                self.save_quotes(niche_name, data)
            logger.info(f"Added {added_count} quotes to {niche_name}")
            return added_count
        except Exception as e:
            logger.error(f"Failed to add quotes to {niche_name}: {e}")
            raise

    def update_quote_status(self, niche_name: str, quote_number: str, new_status: str):
        """Update the status of a quote in a niche"""
        if new_status not in ["generated", "uploaded"]:
            raise ValueError(f"Invalid status: {new_status}")
        data = self.load_quotes(niche_name)
        quotes = data["quotes"]
        try:
            for quote in quotes:
                if quote["number"] == quote_number:
                    quote["status"] = new_status
                    if new_status == "uploaded" and not quote.get("uploaded_date"):
                        quote["uploaded_date"] = datetime.now().strftime("%d-%b-%Y")
                    break
            else:
                raise ValueError(f"Quote #{quote_number} not found in {niche_name}")
            self.save_quotes(niche_name, data)
            logger.info(
                f"Updated status of quote #{quote_number} in {niche_name} to {new_status}"
            )
        except Exception as e:
            logger.error(f"Failed to update quote status in {niche_name}: {e}")
            raise

    def update_quote_content(
        self,
        niche_name: str,
        quote_number: str,
        caption: str = None,
        hashtags: str = None,
    ):
        """Update Instagram content for a quote in a niche"""
        if (caption is not None and not caption.strip()) or (
            hashtags is not None and not hashtags.strip()
        ):
            raise ValueError("Caption or hashtags cannot be empty")
        data = self.load_quotes(niche_name)
        quotes = data["quotes"]
        try:
            for quote in quotes:
                if quote["number"] == quote_number:
                    if caption is not None:
                        quote["caption"] = html.unescape(caption)
                    if hashtags is not None:
                        quote["hashtags"] = html.unescape(hashtags)
                    break
            else:
                raise ValueError(f"Quote #{quote_number} not found in {niche_name}")
            self.save_quotes(niche_name, data)
            logger.info(f"Updated content for quote #{quote_number} in {niche_name}")
        except Exception as e:
            logger.error(f"Failed to update quote content in {niche_name}: {e}")
            raise

    def delete_quote(self, niche_name: str, quote_number: str, password: str) -> bool:
        """Delete a quote from a niche"""
        hashed = os.getenv("PASSWORD_HASH")
        if not hashed or not bcrypt.checkpw(password.encode(), hashed.encode()):
            logger.warning(
                f"Invalid password for deleting quote #{quote_number} in {niche_name}"
            )
            return False
        data = self.load_quotes(niche_name)
        quotes = data["quotes"]
        try:
            quote = next((q for q in quotes if q["number"] == quote_number), None)
            if quote:
                quotes.remove(quote)
                self.save_quotes(niche_name, data)
                logger.info(f"Deleted quote #{quote_number} from {niche_name}")
                return True
            logger.warning(f"Quote #{quote_number} not found in {niche_name}")
            return False
        except Exception as e:
            logger.error(f"Failed to delete quote #{quote_number} in {niche_name}: {e}")
            return False

    def get_quote_by_number(self, niche_name: str, quote_number: str):
        """Retrieve a quote by its number"""
        data = self.load_quotes(niche_name)
        try:
            for quote in data["quotes"]:
                if quote["number"] == quote_number:
                    return quote
            logger.warning(f"Quote #{quote_number} not found in {niche_name}")
            return None
        except Exception as e:
            logger.error(
                f"Failed to retrieve quote #{quote_number} in {niche_name}: {e}"
            )
            return None

    def get_quote_stats(self, niche_name: str):
        """Get statistics about quotes in a niche"""
        data = self.load_quotes(niche_name)
        quotes = data.get("quotes", [])
        try:
            total = len(quotes)
            generated = sum(1 for q in quotes if q["status"] == "generated")
            uploaded = sum(1 for q in quotes if q["status"] == "uploaded")
            logger.debug(
                f"Stats for {niche_name}: Total={total}, Generated={generated}, Uploaded={uploaded}"
            )
            return total, generated, uploaded
        except Exception as e:
            logger.error(f"Failed to get stats for {niche_name}: {e}")
            return 0, 0, 0

    def generate_prompt(self, niche_name: str, num_quotes: int = 3):
        """Generate prompt text for LLM based on niche, including past quotes"""
        niche_data = self.niche_manager.get_niche_data(niche_name)
        if not niche_data:
            raise ValueError(f"Niche {niche_name} not found")
        try:
            # Load base quote prompt
            base_prompt = niche_data["quote_prompt"].format(num_quotes=num_quotes)

            # Load past quotes
            quotes_data = self.load_quotes(niche_name)
            past_quotes = quotes_data.get("quotes", [])
            # Format quotes as ["text1", "text2", ...], escaping special characters
            formatted_quotes = []
            for quote in past_quotes:
                quote_text = quote.get("text")
                if isinstance(quote_text, str) and quote_text.strip():
                    # Escape quotes and special characters for safe inclusion
                    escaped_text = quote_text.replace('"', '\\"')
                    formatted_quotes.append(f'"{escaped_text}"')
                else:
                    logger.warning(f"Invalid quote text in {niche_name}: {quote_text}")

            # Join quotes with commas
            past_quotes_str = (
                f"[{', '.join(formatted_quotes)}]" if formatted_quotes else "[]"
            )

            # Append past quotes to prompt
            prompt = (
                f"{base_prompt}\n"
                f"Each quote must be original and not identical to, a paraphrase of, or closely resembling "
                f"any of the quotes listed after 'PAST QUOTES:' below.\n"
                f"Please provide only the quotes, without any additional text or explanation.\n"
                f"PAST QUOTES: {past_quotes_str}"
            )

            logger.debug(
                f"Generated prompt for {niche_name} with {len(formatted_quotes)} past quotes: {prompt[:50]}..."
            )
            return prompt
        except Exception as e:
            logger.error(f"Failed to generate prompt for {niche_name}: {e}")
            raise
