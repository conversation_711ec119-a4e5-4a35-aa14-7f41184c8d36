import json
import os
import re
import bcrypt
from datetime import datetime
from dotenv import load_dotenv
import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("niche_manager.log", mode="a"),
    ],
)
logger = logging.getLogger(__name__)

load_dotenv()


class NicheManager:
    """Manages niche creation, editing, deletion, and data storage"""

    def __init__(self):
        self.niches_file = "data/niches.json"
        os.makedirs("data", exist_ok=True)
        self._initialize_niches()

    def _initialize_niches(self):
        """Initialize niches.json with default niches if it doesn't exist"""
        if (
            not os.path.exists(self.niches_file)
            or os.path.getsize(self.niches_file) == 0
        ):
            initial_niches = [
                {
                    "name": "Growth Mindset",
                    "prompt_file": "data/prompts/growth_mindset_prompts.json",
                    "quote_file": "data/quotes/growth_mindset_quotes.json",
                },
                {
                    "name": "Business Mindset",
                    "prompt_file": "data/prompts/business_mindset_prompts.json",
                    "quote_file": "data/quotes/business_mindset_quotes.json",
                },
                {
                    "name": "Stoicism",
                    "prompt_file": "data/prompts/stoicism_prompts.json",
                    "quote_file": "data/quotes/stoicism_quotes.json",
                },
            ]
            default_prompts = {
                "Growth Mindset": {
                    "quote_prompt": "Generate {num_quotes} unique, concise motivational quotes (each 10-20 words) for Instagram. Each quote should inspire positivity, resilience, or personal growth, be uplifting, avoid clichés, and be formatted as a single sentence.",
                    "caption_prompt": "Generate an engaging Instagram caption for: '{quote_text}'. Include a hook, context, CTA, and 5-10 relevant hashtags.",
                },
                "Business Mindset": {
                    "quote_prompt": "Generate {num_quotes} unique, concise business-oriented quotes (each 10-20 words) for Instagram. Each quote should inspire ambition, leadership, or success, be professional, and avoid clichés.",
                    "caption_prompt": "Generate an engaging Instagram caption for: '{quote_text}'. Include a hook, professional insight, CTA, and 5-10 business hashtags.",
                },
                "Stoicism": {
                    "quote_prompt": "Generate {num_quotes} unique, concise Stoic quotes (each 10-20 words) for Instagram. Each quote should reflect wisdom, resilience, or virtue, be contemplative, and avoid clichés.",
                    "caption_prompt": "Generate an engaging Instagram caption for: '{quote_text}'. Include a reflective hook, insight, CTA, and 5-10 Stoic hashtags.",
                },
            }
            try:
                with open(self.niches_file, "w") as f:
                    json.dump(initial_niches, f, indent=4)
                for niche in initial_niches:
                    prompt_file = niche["prompt_file"]
                    quote_file = niche["quote_file"]
                    if (
                        not os.path.exists(prompt_file)
                        or os.path.getsize(prompt_file) == 0
                    ):
                        with open(prompt_file, "w") as f:
                            json.dump(default_prompts[niche["name"]], f, indent=4)
                    if (
                        not os.path.exists(quote_file)
                        or os.path.getsize(quote_file) == 0
                    ):
                        with open(quote_file, "w") as f:
                            json.dump({"quotes": []}, f, indent=4)
                logger.info("Initialized niches and prompt/quote files")
            except Exception as e:
                logger.error(f"Error initializing niches: {e}")
                raise

    def load_niches(self):
        """Load niches from niches.json"""
        try:
            if not os.path.exists(self.niches_file):
                self._initialize_niches()
            with open(self.niches_file, "r") as f:
                niches = json.load(f)
                logger.debug(f"Loaded {len(niches)} niches")
                return niches
        except Exception as e:
            logger.error(f"Error loading niches: {e}")
            return []

    def get_niche_data(self, niche_name: str):
        """Retrieve niche data including prompts"""
        niches = self.load_niches()
        niche = next((n for n in niches if n["name"] == niche_name), None)
        if niche and os.path.exists(niche["prompt_file"]):
            try:
                with open(niche["prompt_file"], "r") as f:
                    data = json.load(f)
                    return {
                        "name": niche_name,
                        **data,
                        "quote_file": niche["quote_file"],
                    }
            except Exception as e:
                logger.error(f"Error loading niche data for {niche_name}: {e}")
        return None

    def validate_niche_name(self, name: str, exclude: str = None) -> bool:
        """Validate niche name (max 15 chars, alphanumeric/spaces, unique)"""
        if (
            not name
            or not re.match(r"^[a-zA-Z0-9 ]+$", name)
            or len(name) > 15
            or not name.strip()
        ):
            logger.warning(f"Invalid niche name: {name}")
            return False
        niches = self.load_niches()
        return all(n["name"] != name for n in niches if n["name"] != exclude)

    def add_niche(self, name: str, quote_prompt: str, caption_prompt: str):
        """Add a new niche with prompts and quote file"""
        if (
            not self.validate_niche_name(name)
            or not quote_prompt.strip()
            or not caption_prompt.strip()
        ):
            raise ValueError("Invalid niche name or empty prompts")
        niches = self.load_niches()
        prompt_file = f"data/prompts/{name.lower().replace(' ', '_')}_prompts.json"
        quote_file = f"data/quotes/{name.lower().replace(' ', '_')}_quotes.json"
        try:
            with open(prompt_file, "w") as f:
                json.dump(
                    {"quote_prompt": quote_prompt, "caption_prompt": caption_prompt},
                    f,
                    indent=4,
                )
            with open(quote_file, "w") as f:
                json.dump({"quotes": []}, f, indent=4)
            niches.append(
                {"name": name, "prompt_file": prompt_file, "quote_file": quote_file}
            )
            with open(self.niches_file, "w") as f:
                json.dump(niches, f, indent=4)
            logger.info(f"Added niche {name}")
        except Exception as e:
            logger.error(f"Error adding niche {name}: {e}")
            raise

    def update_niche(
        self, old_name: str, new_name: str, quote_prompt: str, caption_prompt: str
    ):
        """Update an existing niche"""
        if (
            not self.validate_niche_name(new_name, exclude=old_name)
            or not quote_prompt.strip()
            or not caption_prompt.strip()
        ):
            raise ValueError("Invalid niche name or empty prompts")
        niches = self.load_niches()
        niche = next((n for n in niches if n["name"] == old_name), None)
        if not niche:
            raise ValueError(f"Niche {old_name} not found")
        old_prompt_file = niche["prompt_file"]
        old_quote_file = niche["quote_file"]
        new_prompt_file = f"data/prompts/{new_name.lower().replace(' ', '_')}_prompts.json"
        new_quote_file = f"data/quotes/{new_name.lower().replace(' ', '_')}_quotes.json"
        try:
            with open(new_prompt_file, "w") as f:
                json.dump(
                    {"quote_prompt": quote_prompt, "caption_prompt": caption_prompt},
                    f,
                    indent=4,
                )
            if old_quote_file != new_quote_file:
                if os.path.exists(old_quote_file):
                    os.rename(old_quote_file, new_quote_file)
                else:
                    with open(new_quote_file, "w") as f:
                        json.dump({"quotes": []}, f, indent=4)
            niche["name"] = new_name
            niche["prompt_file"] = new_prompt_file
            niche["quote_file"] = new_quote_file
            with open(self.niches_file, "w") as f:
                json.dump(niches, f, indent=4)
            if old_prompt_file != new_prompt_file and os.path.exists(old_prompt_file):
                os.remove(old_prompt_file)
            logger.info(f"Updated niche '{old_name}' to '{new_name}'")
        except Exception as e:
            logger.error(f"Error updating niche {old_name}: {e}")
            raise

    def delete_niche(self, name: str, password: str) -> bool:
        """Delete a niche and its associated files"""
        hashed_password = os.getenv("PASSWORD_HASH")
        if not hashed_password or not bcrypt.checkpw(
            password.encode(), hashed_password.encode()
        ):
            logger.warning(f"Invalid password attempt for deleting niche {name}")
            return False
        niches = self.load_niches()
        try:
            niche = next((n for n in niches if n["name"] == name), None)
            if niche:
                quote_file = niche["quote_file"]
                prompt_file = niche["prompt_file"]
                backup_path = self.create_backup(quote_file)
                if os.path.exists(prompt_file):
                    os.remove(prompt_file)
                if os.path.exists(quote_file):
                    os.remove(quote_file)
                niches.remove(niche)
                with open(self.niches_file, "w") as f:
                    json.dump(niches, f, indent=4)
                logger.info(f"Deleted niche {name}, backup: {backup_path}")
                return True
            logger.warning(f"Niche {name} not found")
            return False
        except Exception as e:
            logger.error(f"Failed to delete niche {name}: {e}")
            return False

    def manual_backup_all(self):
        """Manually back up all niches' quote files"""
        niches = self.load_niches()
        backed_files = []
        try:
            for niche_data in niches:
                quote_file = niche_data.get("quote_file")
                if quote_file:
                    backup_path = self.create_backup(quote_file)
                    if backup_path:
                        backed_files.append(backup_path)
            if backed_files:
                logger.info(
                    f"Manually backed up {len(backed_files)} niches' quotes: {backed_files}"
                )
                return backed_files
            logger.warning("No files backed up")
            return []
        except Exception as e:
            logger.error(f"Error during manual backup: {e}")
            return []

    def create_backup(self, quote_file: str) -> str:
        """Create a backup of a quote file with timestamp"""
        if os.path.exists(quote_file) and os.path.getsize(quote_file) > 0:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")
            backup_file = f"backups/{os.path.basename(quote_file).replace('.json', '')}_backup_{timestamp}.json"
            os.makedirs("backups", exist_ok=True)
            try:
                with open(quote_file, "r") as src, open(backup_file, "w") as dst:
                    dst.write(src.read())
                logger.info(f"Created backup: {backup_file}")
                return backup_file
            except Exception as e:
                logger.error(f"Failed to create backup for {quote_file}: {e}")
                return ""
        return ""
