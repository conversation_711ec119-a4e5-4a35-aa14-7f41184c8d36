Instagram Quote Generator
A Streamlit app for generating, managing, and formatting Instagram quotes across different niches.
Setup Instructions

Clone the Repository:
git clone <repository-url>
cd quote-gen


Create Directory Structure:Ensure the following subdirectories exist:
mkdir -p managers ui scripts data backups
touch managers/__init__.py ui/__init__.py scripts/__init__.py


Install Dependencies:
pip install -r requirements.txt


Set Up Environment:

Create a .env file in the root directory.
Run the password setup script:python scripts/setup_password.py


Copy the generated PASSWORD_HASH to .env:PASSWORD_HASH=<your-hash-here>




Run the App:
streamlit run main.py



Features

Niche Management: Select, edit, add, or delete niches with custom prompts and quote files. Manual backup for all niches and automatic backup on niche deletion.
Quote Generator: Generate LLM prompts, copy them, and save quotes with an edit/save workflow. Prevents saving empty quotes.
Quote Management: View all quotes in the selected niche with delete option (password-protected). Update quote status and preview Instagram posts.
Caption Generator: Generate caption prompts, edit/save captions and hashtags, and mark quotes as uploaded. Prevents saving empty fields.
Security: Password-protected niche and quote deletion using bcrypt.
Backup: Stores backups in backups/ with timestamps (e.g., growth_mindset_quotes_backup_20250610_1830.json).

File Structure
quote-gen/
├── backups/
│   ├── *_backup_*.json
├── data/
│   ├── niches.json
│   ├── growth_mindset_prompts.json
│   ├── growth_mindset_quotes.json
│   ├── business_mindset_prompts.json
│   ├── business_mindset_quotes.json
│   ├── stoicism_prompts.json
│   ├── stoicism_quotes.json
├── managers/
│   ├── __init__.py
│   ├── niche_manager.py
│   ├── quote_manager.py
│   ├── tab_manager.py
├── ui/
│   ├── __init__.py
│   ├── components.py
├── scripts/
│   ├── __init__.py
│   ├── setup_password.py
├── .env
├── main.py
├── requirements.txt
├── app.log
├── niche_manager.log
├── quote_manager.log
├── tab_manager.log
└── README.md

Usage

Niche Management: Select a niche, edit its details, add/delete niches with password protection, or generate manual backups.
Quote Generator: Generate a prompt, copy it, edit and save new quotes. View quote statistics.
Quote Management: View all quotes in the selected niche, update status, delete quotes, or preview Instagram posts.
Caption Generator: Generate caption prompts, edit/save Instagram content, mark quotes as uploaded, and preview posts.

Testing Notes

Verify niche selection persists across tabs.
Test edit/save for quotes and captions, ensuring no empty fields are saved.
Check quote and niche deletion with password protection.
Ensure only the selected niche’s quotes are shown in Quote Management.
Validate manual and automatic backups in backups/.
Test responsiveness on mobile and desktop.
Check logs (app.log, niche_manager.log, etc.) for errors.

Troubleshooting

App doesn’t start: Ensure dependencies are installed and .env has PASSWORD_HASH. Check app.log for errors.
Missing files: The app creates data/ and JSON files on first run. Ensure write permissions.
Password issues: Rerun setup_password.py to generate a new hash if the password doesn’t work.

For issues, consult logs or contact the developer with error details.
