import streamlit as st
import html
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("components.log", mode="a"),
    ],
)
logger = logging.getLogger(__name__)


class UIComponents:
    """UI components for rendering stats, quotes, and Instagram previews.
    Depends on CSS classes (.stat-card, .quote-preview, .instagram-preview) from main.py.
    """

    @staticmethod
    def render_stat_card(title: str, value: int, key: str):
        """Render a single stat card for a metric (e.g., Total Quotes)"""
        try:
            st.markdown(
                f'<div class="stat-card"><b>{html.escape(title)}</b><br>{value}</div>',
                unsafe_allow_html=True,
            )
            logger.debug(f"Rendered stat card: {title} with key {key}")
        except Exception as e:
            st.error(f"Failed to render stat card: {e}")
            logger.error(f"Error rendering stat card {title}: {e}")

    @staticmethod
    def render_quote_preview(quote: dict, key: str):
        """Render a preview of a single quote"""
        try:
            if not isinstance(quote, dict):
                logger.warning(f"Invalid quote data: {quote}, expected dictionary")
                st.error("Invalid quote data provided.")
                return

            # Safely extract values with defaults, ensuring strings
            quote_text = quote.get("text")
            quote_text = (
                str(quote_text) if quote_text is not None else "No quote text available"
            )
            quote_text = html.escape(html.unescape(quote_text))

            quote_number = quote.get("number")
            quote_number = str(quote_number) if quote_number is not None else "N/A"
            quote_number = html.escape(quote_number)

            status = quote.get("status")
            status = str(status).title() if status is not None else "Unknown"
            status = html.escape(status)

            created_date = quote.get("created_date")
            created_date = str(created_date) if created_date is not None else "N/A"
            created_date = html.escape(created_date)

            uploaded_date = quote.get("uploaded_date")
            uploaded_date = str(uploaded_date) if uploaded_date is not None else "None"
            uploaded_date = html.escape(uploaded_date)

            # Log warnings for None values
            if None in (
                quote.get("text"),
                quote.get("number"),
                quote.get("status"),
                quote.get("created_date"),
            ):
                logger.warning(f"Quote contains None values: {quote}")

            st.markdown(
                f"""
                <div class="quote-preview">
                    <p><strong>Quote:</strong> #{quote_number} - {status}</p>
                    <div>{quote_text}</div>
                    <p><strong>Created:</strong> {created_date}</p>
                    <p><strong>Uploaded:</strong> {uploaded_date}</p>
                </div>
                """,
                unsafe_allow_html=True,
            )
            logger.debug(
                f"Rendered quote preview for quote #{quote_number} with key {key}"
            )
        except Exception as e:
            st.error(f"Failed to render quote preview: {e}")
            logger.error(f"Error rendering quote preview for key {key}: {e}")

    @staticmethod
    def render_instagram_preview(quote: dict, key: str):
        """Render a preview of an Instagram post"""
        try:
            if not isinstance(quote, dict):
                logger.warning(f"Invalid quote data: {quote}, expected dictionary")
                st.error("Invalid quote data provided.")
                return

            # Safely extract values with defaults, ensuring strings
            quote_text = quote.get("text")
            quote_text = (
                str(quote_text) if quote_text is not None else "No quote available"
            )
            quote_text = html.escape(html.unescape(quote_text))

            caption = quote.get("caption")
            caption = str(caption) if caption is not None else "No caption provided"
            caption = html.escape(html.unescape(caption))

            hashtags = quote.get("hashtags")
            hashtags = str(hashtags) if hashtags is not None else "No hashtags provided"
            hashtags = html.escape(html.unescape(hashtags))

            # Log warnings for None values
            if None in (quote.get("text"), quote.get("caption"), quote.get("hashtags")):
                logger.warning(f"Quote contains None values: {quote}")

            st.markdown(
                f"""
                <div class="instagram-preview">
                    <p><strong>Quote:</strong> {quote_text}</p>
                    <p><strong>Caption:</strong> {caption}</p>
                    <p><strong>Hashtags:</strong> {hashtags}</p>
                </div>
                """,
                unsafe_allow_html=True,
            )
            logger.debug(
                f"Rendered Instagram preview for quote #{quote.get('number', 'N/A')} with key {key}"
            )
        except Exception as e:
            st.error(f"Failed to render Instagram preview: {e}")
            logger.error(f"Error rendering Instagram preview for key {key}: {e}")
